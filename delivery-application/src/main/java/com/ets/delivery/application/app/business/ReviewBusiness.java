package com.ets.delivery.application.app.business;

import com.ets.delivery.application.common.dto.reviews.RiskResultNotifyDTO;
import com.ets.delivery.application.infra.entity.Reviews;
import com.ets.delivery.application.infra.service.ReviewsService;
import com.ets.delivery.application.common.consts.reviews.ReviewsRiskStatusEnum;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewCreateDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import java.time.LocalDateTime;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ReviewBusiness {

    @Value("${review.risk_review_handle:false}")
    private boolean riskReviewHandleEnabled;

    @Autowired
    private ReviewsService reviewsService;

    @Autowired
    private RiskReviewBusiness riskReviewBusiness;

    public void riskResultNotify(RiskResultNotifyDTO notifyDTO) {
        // 通过业务单号查询待风控审核单
        Reviews reviews = reviewsService.getOneByOrderSnAndRiskStatus(notifyDTO.getBusinessSn(), ReviewsRiskStatusEnum.PENDING.getValue());
        if (reviews == null) {
            log.warn("未找到对应的审核单，businessSn:{} riskStatus:{}", notifyDTO.getBusinessSn(), notifyDTO.getRiskStatus());
            return;
        }

        // 满足条件，风控状态直接置为风控通过
        if (!riskReviewHandleEnabled) {
            reviews.setRiskStatus(ReviewsRiskStatusEnum.APPROVED.getValue());
            reviews.setUpdatedAt(LocalDateTime.now());
            reviewsService.updateById(reviews);
            log.info("配置关闭风控处理，直接将风控状态置为风控通过，orderSn:{} riskStatus:{}", notifyDTO.getBusinessSn(), ReviewsRiskStatusEnum.APPROVED.getValue());

            // TODO 请求申办审核处理审核单
            return;
        }

        // 风控通过
        if (notifyDTO.getRiskStatus().equals(1)) {
            reviews.setRiskStatus(ReviewsRiskStatusEnum.APPROVED.getValue());
            reviews.setUpdatedAt(LocalDateTime.now());
            reviewsService.updateById(reviews);
            log.info("风控通过，已更新审核单状态并待通知，orderSn:{}", notifyDTO.getBusinessSn());

            // TODO 请求申办审核处理审核单
            return;
        }

        // 风控不通过，且允许处理风控结果时才创建风控初审单
        if (notifyDTO.getRiskStatus().equals(2)) {
            RiskReviewCreateDTO createDTO = new RiskReviewCreateDTO();
            createDTO.setBusinessSn(notifyDTO.getBusinessSn());
            createDTO.setBusinessType(notifyDTO.getBusinessType());
            createDTO.setRiskSn(notifyDTO.getRiskSn());
            createDTO.setRiskType(1); // 初审
            createDTO.setIssuerId(reviews.getIssuerId());
            createDTO.setUid(reviews.getUid());
            createDTO.setPlateNo(reviews.getPlateNo());
            Integer plateColor = 0;
            try {
                if (reviews.getPlateColor() != null && !reviews.getPlateColor().isEmpty()) {
                    plateColor = Integer.valueOf(reviews.getPlateColor());
                }
            } catch (Exception e) {
                log.warn("plateColor转换失败，原值:{}", reviews.getPlateColor());
            }
            createDTO.setPlateColor(plateColor);
            riskReviewBusiness.createRiskReview(createDTO);
            log.info("风控不通过，已创建风控初审单，orderSn:{}", notifyDTO.getBusinessSn());
            return;
        }

        log.info("风控结果通知处理结束，businessSn:{} riskStatus:{}", notifyDTO.getBusinessSn(), notifyDTO.getRiskStatus());
    }
}
