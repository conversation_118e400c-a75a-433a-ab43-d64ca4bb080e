package com.ets.delivery.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewListDTO;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviews;
import com.ets.delivery.application.infra.mapper.RiskReviewsMapper;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 风控审核单记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class RiskReviewsService extends BaseService<RiskReviewsMapper, RiskReviews> {

    public IPage<RiskReviews> getList(RiskReviewListDTO listDTO) {

        Wrapper<RiskReviews> wrapper = Wrappers.<RiskReviews>lambdaQuery()
                .eq(listDTO.getRiskType() != null, RiskReviews::getRiskType, listDTO.getRiskType())
                .eq(listDTO.getIssuerId() != null, RiskReviews::getIssuerId, listDTO.getIssuerId())
                .eq(listDTO.getUid() != null, RiskReviews::getUid, listDTO.getUid())
                .eq(listDTO.getPlateNo() != null, RiskReviews::getPlateNo, listDTO.getPlateNo())
                .eq(listDTO.getBusinessSn() != null, RiskReviews::getBusinessSn, listDTO.getBusinessSn())
                .eq(listDTO.getRiskReviewSn() != null, RiskReviews::getRiskReviewSn, listDTO.getRiskReviewSn())
                .eq(listDTO.getRiskReviewStatus() != null, RiskReviews::getRiskReviewStatus, listDTO.getRiskReviewStatus())
                .eq(listDTO.getRejectReasonId() != null, RiskReviews::getRejectReasonId, listDTO.getRejectReasonId())
                .eq(listDTO.getAutoAudit() != null, RiskReviews::getAutoAudit, listDTO.getAutoAudit())
                .ge(ObjectUtils.isNotEmpty(listDTO.getCreateStartTime()), RiskReviews::getCreatedAt, listDTO.getCreateStartTime())
                .le(ObjectUtils.isNotEmpty(listDTO.getCreateEndTime()), RiskReviews::getCreatedAt, listDTO.getCreateEndTime())
                .like(StringUtils.isNotEmpty(listDTO.getRiskRuleId()), RiskReviews::getRiskRuleIds, "\"" + listDTO.getRiskRuleId() + "\"");

        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    /**
     * 根据业务单号查询风控审核单
     *
     * @param businessSn 业务单号
     * @return 风控审核单
     */
    public RiskReviews getByBusinessSn(String businessSn) {
        Wrapper<RiskReviews> wrapper = Wrappers.<RiskReviews>lambdaQuery()
                .eq(RiskReviews::getBusinessSn, businessSn)
                .orderByDesc(RiskReviews::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据ID查询风控审核单
     *
     * @param id 主键ID
     * @return 风控审核单
     */
    public RiskReviews getById(Integer id) {
        return this.baseMapper.selectById(id);
    }

    /**
     * 根据风控单号查询风控审核单
     *
     * @param riskReviewSn 风控单号
     * @return 风控审核单
     */
    public RiskReviews getByRiskReviewSn(String riskReviewSn) {
        Wrapper<RiskReviews> wrapper = Wrappers.<RiskReviews>lambdaQuery()
                .eq(RiskReviews::getRiskReviewSn, riskReviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
