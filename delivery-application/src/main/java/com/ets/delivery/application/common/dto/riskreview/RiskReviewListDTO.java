package com.ets.delivery.application.common.dto.riskreview;

import com.ets.delivery.application.common.consts.riskreview.RiskReviewRiskTypeEnum;
import jakarta.validation.constraints.AssertTrue;
import lombok.Data;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

@Data
public class RiskReviewListDTO {
    /** 风控类型[1-初审 2-复审] */
    private Integer riskType;
    /** 省份（发卡方ID） */
    private Integer issuerId;
    /** 用户id */
    private Long uid;
    /** 车牌号 */
    private String plateNo;
    /** 业务单号 */
    private String businessSn;
    /** 风控单号 */
    private String riskReviewSn;
    /** 审核状态 */
    private Integer riskReviewStatus;
    /** 驳回原因id */
    private Integer rejectReasonId;
    /** 风控规则ID */
    private String riskRuleId;
    /** 是否自动审核 */
    private Integer autoAudit;
    /** 创建时间-开始 */
    private String createStartTime;
    /** 创建时间-结束 */
    private String createEndTime;
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;

    @AssertTrue(message = "风控类型不正确")
    public boolean isValidRiskType() {
        return this.riskType == null || RiskReviewRiskTypeEnum.isValidValue(this.riskType);
    }
} 