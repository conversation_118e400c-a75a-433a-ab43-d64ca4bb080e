package com.ets.delivery.application.controller.notify;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.ReviewBusiness;
import com.ets.delivery.application.common.dto.reviews.RiskResultNotifyDTO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/notify/review")
public class NotifyReviewController {

    @Autowired
    private ReviewBusiness reviewBusiness;

    /**
     * 风险结果通知
     *
     * @param notifyDTO 通知请求参数
     * @return 通知结果
     */
    @PostMapping("/risk-result-notify")
    public JsonResult<Void> riskResultNotify(@RequestBody @Valid RiskResultNotifyDTO notifyDTO) {
        reviewBusiness.riskResultNotify(notifyDTO);
        return JsonResult.ok();
    }

}
