package com.ets.delivery.application.controller.nologin;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.RiskReviewBusiness;
import com.ets.delivery.application.common.dto.riskreview.RiskInfoUploadDTO;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewCancelDTO;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewCreateDTO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 风险审核订单
 */
@RestController
@RequestMapping("/risk-review")
public class RiskReviewController {

    @Autowired
    private RiskReviewBusiness riskReviewBusiness;

    /**
     * 创建风险审核订单
     *
     * @param createDTO 创建风险审核订单请求参数
     * @return 创建结果
     */
    @PostMapping("/create")
    public JsonResult<String> create(@RequestBody @Valid RiskReviewCreateDTO createDTO) {
        String riskOrderSn = riskReviewBusiness.createRiskReview(createDTO);
        return JsonResult.ok(riskOrderSn);
    }

    /**
     * 取消风险审核订单
     *
     * @param cancelDTO 取消风险审核订单请求参数
     * @return 取消结果
     */
    @PostMapping("/cancel")
    public JsonResult<Void> cancel(@RequestBody @Valid RiskReviewCancelDTO cancelDTO) {
        riskReviewBusiness.cancelRiskReview(cancelDTO);
        return JsonResult.ok();
    }

    /**
     * 上传风险信息
     *
     * @param infoUploadDTO 风险信息上传请求参数
     * @return 上传结果
     */
    @PostMapping("/risk-info-upload")
    public JsonResult<Void> riskInfoUpload(@RequestBody @Valid RiskInfoUploadDTO infoUploadDTO) {
        riskReviewBusiness.riskInfoUpload(infoUploadDTO);
        return JsonResult.ok();
    }

}
