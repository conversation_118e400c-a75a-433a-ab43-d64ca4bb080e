package com.ets.delivery.application.infra.entity.riskreview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.delivery.application.infra.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 风控审核单记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@TableName("etc_risk_reviews_order")
public class RiskReviews extends BaseEntity<RiskReviews> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 风控单号
     */
    private String riskReviewSn;

    /**
     * 风控流水号
     */
    private String riskSn;

    /**
     * 风控规则ID集合（逗号分隔）
     */
    private String riskRuleIds;

    /**
     * 风控类型[1-初审 2-复审]
     */
    private Integer riskType;

    /**
     * 业务类型[1-申办]
     */
    private Integer businessType;

    /**
     * 业务单号
     */
    private String businessSn;

    /**
     * 发卡方id
     */
    private Integer issuerId;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 风控审核状态[0-待审核 1-审核通过 2-审核驳回取消 3-审核驳回重新上传 4-补传资料 5-审核取消]
     */
    private Integer riskReviewStatus;

    /**
     * 驳回原因id
     */
    private Integer rejectReasonId;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 风控审核时间
     */
    private LocalDateTime riskReviewTime;

    /**
     * 风控审核单备注
     */
    private String riskReviewRemark;

    private Integer autoAudit;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
