package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.RiskReviewBusiness;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewDetailDTO;
import com.ets.delivery.application.common.dto.riskreview.RiskReveiewListDTO;
import com.ets.delivery.application.common.dto.riskreview.RiskReveiewLogDTO;

import com.ets.delivery.application.common.vo.riskreview.RiskReviewListVO;
import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/risk-reviews")
public class AdminRiskReviewsController {

    @Autowired
    private RiskReviewBusiness riskReviewBusiness;

    @PostMapping("/get-list")
    public JsonResult<IPage<RiskReviewListVO>> getList(@RequestBody @Valid RiskReveiewListDTO listDTO) {
        return JsonResult.ok(riskReviewBusiness.getList(listDTO));
    }

    @PostMapping("/get-detail")
    public JsonResult<?> getDetail(@RequestBody @Valid RiskReviewDetailDTO detailDTO) {
        return JsonResult.ok();
    }

    @PostMapping("/get-log")
    public JsonResult<?> getLog(@RequestBody @Valid RiskReveiewLogDTO logDTO) {
        return JsonResult.ok();
    }

    @PostMapping("/get-preview")
    public JsonResult<?> getPreview() {
        return JsonResult.ok();
    }

    @PostMapping("/submit-preview")
    public JsonResult<?> submitPreview() {
        return JsonResult.ok();
    }

    @PostMapping("/get-recheck")
    public JsonResult<?> getRecheck() {
        return JsonResult.ok();
    }

    @PostMapping("/submit-recheck")
    public JsonResult<?> submitRecheck() {
        return JsonResult.ok();
    }

}
