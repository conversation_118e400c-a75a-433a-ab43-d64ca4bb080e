package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.RiskReviewBusiness;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewDetailDTO;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewListDTO;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewLogListDTO;

import com.ets.delivery.application.common.vo.riskreview.RiskReviewDetailVO;
import com.ets.delivery.application.common.vo.riskreview.RiskReviewListVO;
import com.ets.delivery.application.common.vo.riskreview.RiskReviewLogListVO;
import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/risk-reviews")
public class AdminRiskReviewsController {

    @Autowired
    private RiskReviewBusiness riskReviewBusiness;

    @PostMapping("/get-list")
    public JsonResult<IPage<RiskReviewListVO>> getList(@RequestBody @Valid RiskReviewListDTO listDTO) {
        return JsonResult.ok(riskReviewBusiness.getList(listDTO));
    }

    @PostMapping("/get-detail")
    public JsonResult<?> getDetail(@RequestBody @Valid RiskReviewDetailDTO detailDTO) {
        return JsonResult.ok(riskReviewBusiness.getDetail(detailDTO));
    }

    @PostMapping("/get-log")
    public JsonResult<?> getLog(@RequestBody @Valid RiskReviewLogListDTO logListDTO) {
        return JsonResult.ok(riskReviewBusiness.getLog(logListDTO));
    }

    @PostMapping("/get-preview")
    public JsonResult<?> getPreview() {
        return JsonResult.ok();
    }

    @PostMapping("/submit-preview")
    public JsonResult<?> submitPreview() {
        return JsonResult.ok();
    }

    @PostMapping("/get-recheck")
    public JsonResult<?> getRecheck() {
        return JsonResult.ok();
    }

    @PostMapping("/submit-recheck")
    public JsonResult<?> submitRecheck() {
        return JsonResult.ok();
    }

}
