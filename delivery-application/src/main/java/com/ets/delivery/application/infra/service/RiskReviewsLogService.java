package com.ets.delivery.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewLogListDTO;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsLog;
import com.ets.delivery.application.infra.mapper.RiskReviewsLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 风控审核单日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class RiskReviewsLogService extends BaseService<RiskReviewsLogMapper, RiskReviewsLog> {

    /**
     * 根据风控审核单ID分页查询日志
     *
     * @param logDTO 查询参数
     * @param riskReviewSn 风控审核单号
     * @return 分页日志列表
     */
    public IPage<RiskReviewsLog> getLogList(RiskReviewLogListDTO logDTO, String riskReviewSn) {
        Wrapper<RiskReviewsLog> wrapper = Wrappers.<RiskReviewsLog>lambdaQuery()
                .eq(RiskReviewsLog::getRiskReviewSn, riskReviewSn)
                .orderByDesc(RiskReviewsLog::getCreatedAt);

        return this.baseMapper.selectPage(new Page<>(logDTO.getPageNum(), logDTO.getPageSize()), wrapper);
    }
}
