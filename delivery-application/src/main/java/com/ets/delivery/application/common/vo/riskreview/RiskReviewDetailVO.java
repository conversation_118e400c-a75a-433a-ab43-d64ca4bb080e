package com.ets.delivery.application.common.vo.riskreview;

import com.ets.delivery.application.common.consts.riskreview.RiskReviewRiskTypeEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 风控审核详情VO
 */
@Data
public class RiskReviewDetailVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 风控单号
     */
    private String riskReviewSn;

    /**
     * 风控流水号
     */
    private String riskSn;

    /**
     * 风控规则ID集合（逗号分隔）
     */
    private String riskRuleIds;

    /**
     * 风控类型[1-初审 2-复审]
     */
    private Integer riskType;

    private String riskTypeStr;

    /**
     * 业务类型[1-申办]
     */
    private Integer businessType;

    /**
     * 业务单号
     */
    private String businessSn;

    /**
     * 发卡方id
     */
    private Integer issuerId;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private Integer plateColor;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 风控审核状态[0-待审核 1-审核通过 2-审核驳回取消 3-审核驳回重新上传 4-补传资料 5-审核取消]
     */
    private Integer riskReviewStatus;

    private String riskReviewStatusStr;

    /**
     * 驳回原因id
     */
    private Integer rejectReasonId;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 风控审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime riskReviewTime;

    /**
     * 风控审核单备注
     */
    private String riskReviewRemark;

    /**
     * 是否自动审核
     */
    private Integer autoAudit;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 补充图片url
     */
    private String additionalImgUrl;

    /**
     * 业务审核单号
     */
    private String reviewSn;

    public String getRiskTypeStr() {
        return RiskReviewRiskTypeEnum.map.getOrDefault(this.riskType, "未知");
    }

    public String getRiskReviewStatusStr() {
        return RiskReviewStatusEnum.map.getOrDefault(this.riskReviewStatus, "未知");
    }
}
