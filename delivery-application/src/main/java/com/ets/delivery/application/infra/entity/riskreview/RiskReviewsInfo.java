package com.ets.delivery.application.infra.entity.riskreview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.delivery.application.infra.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 风控资料记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@TableName("etc_risk_reviews_order_info")
public class RiskReviewsInfo extends BaseEntity<RiskReviewsInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 风控单号
     */
    private String riskReviewSn;

    /**
     * 业务审核单号
     */
    private String reviewSn;

    /**
     * 补充图片url
     */
    private String additionalImgUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
