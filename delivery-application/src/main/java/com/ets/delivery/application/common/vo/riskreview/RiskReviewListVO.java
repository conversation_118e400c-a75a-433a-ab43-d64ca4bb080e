package com.ets.delivery.application.common.vo.riskreview;

import lombok.Data;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

@Data
public class RiskReviewListVO {
    private Integer id;
    private String riskReviewSn;
    private Integer riskType;
    private String riskTypeStr;
    private String businessSn;
    private Integer issuerId;
    private String plateNo;
    private Integer plateColor;
    private Integer riskReviewStatus;
    private String riskReviewStatusStr;
    private String riskRuleIds;
    private String riskRuleRemark;
    private Integer autoAudit;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime riskReviewTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
} 