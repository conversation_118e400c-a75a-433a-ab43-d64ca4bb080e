package com.ets.delivery.application.common.dto.riskreview;

import lombok.Data;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

@Data
public class RiskReviewLogDTO {
    @NotNull(message = "id不能为空")
    private Integer id;

    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}