package com.ets.delivery.application.common.vo.riskreview;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 风控审核日志列表VO
 */
@Data
public class RiskReviewLogListVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 风控审核单号
     */
    private String riskReviewSn;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
